# ESP32S3 Motor Controller Desktop App

一个基于Electron的桌面应用程序，用于远程控制ESP32S3震动电机驱动器。

## 功能特性

- **10x10 PWM控制矩阵**：直接输入控制100个电机的PWM值（0-255）
- **实时连接状态**：显示与ESP32S3设备的连接状态
- **批量控制**：支持预设值应用到所有电机
- **增量更新**：只发送与上次相比有变化的PWM值，减少网络传输
- **远程控制**：通过UDP协议与设备通讯
- **详细通讯日志**：记录所有发送/接收的UDP消息
- **设备状态监控**：实时显示设备运行状态
- **输入验证**：自动验证PWM值范围并提供视觉反馈

## 安装和运行

### 前置要求

- Node.js (版本 16 或更高)
- npm 或 yarn

### 安装步骤

1. 克隆或下载项目文件
2. 在项目目录中安装依赖：

```bash
cd motor-controller-app
npm install
```

### 运行应用

#### 开发模式
```bash
npm run dev
```

#### 生产模式
```bash
npm start
```

#### 构建可执行文件
```bash
npm run build
```

## 使用说明

### 1. 连接设备

1. 确保ESP32S3设备已连接到WiFi网络
2. 在应用中输入设备的IP地址（默认：*************）
3. 确认端口号为3000
4. 点击"Test Connection"按钮测试连接

### 2. 控制电机

#### PWM值设置
- 使用10x10矩阵中的输入框直接输入每个电机的PWM值（0-255）
- 电机编号从M0到M99
- 输入值为0时电机关闭，255时为最大强度
- 自动验证输入范围，超出范围会显示红色警告

#### 批量操作
- **预设值应用**：使用预设滑块设置一个值，然后点击"Apply to All"应用到所有电机
- **清除所有**：点击"Clear All"将所有电机PWM值设为0

#### 发送和控制
1. **Send PWM**：使用增量更新发送PWM值，只发送与上次相比有变化的值
2. **Reset Base**：重置增量比较基准，将当前状态设为已发送状态
3. **START**：启动所有电机，执行已发送的PWM值
4. **STOP**：立即停止所有电机输出

#### 增量更新功能
- 系统会自动比较当前PWM值与上次发送的值
- 只发送有变化的电机PWM值，大幅减少网络传输量
- 在通讯日志中显示详细的变化信息（如：M5: 0→128）
- 使用"Reset Base"按钮可以重新设置比较基准

### 3. 监控状态

- **连接状态**：顶部显示与设备的连接状态
- **设备信息**：显示设备的运行状态、WiFi连接、可用芯片数等
- **通讯日志**：详细记录所有UDP通讯消息
  - 蓝色：发送的消息
  - 绿色：接收的响应
  - 红色：错误信息
  - 支持过滤显示不同类型的消息

## UDP通讯协议

应用使用UDP协议与ESP32S3设备通讯，支持以下命令：

- `START` - 启动所有电机
- `STOP` - 停止所有电机
- `STATUS` - 查询设备状态
- `SET,motor,pwm` - 设置指定电机的PWM值

所有响应均为JSON格式，包含操作结果和设备状态信息。

## 故障排除

### 连接问题
1. 确认ESP32S3设备已正确连接到WiFi
2. 检查IP地址是否正确
3. 确认防火墙没有阻止UDP通讯
4. 验证设备和电脑在同一网络中

### 控制问题
1. 确保先测试连接成功
2. 发送PWM值后需要点击START才能执行
3. 检查活动日志中的错误信息

### 性能优化
1. 避免频繁发送大量PWM值
2. 使用预设值功能进行批量设置
3. 定期清理活动日志

## 开发信息

### 技术栈
- **Electron**：跨平台桌面应用框架
- **Node.js**：后端运行时
- **HTML/CSS/JavaScript**：前端界面
- **UDP**：网络通讯协议

### 项目结构
```
motor-controller-app/
├── main.js          # Electron主进程
├── renderer.js      # 渲染进程逻辑
├── index.html       # 主界面
├── style.css        # 样式文件
├── package.json     # 项目配置
└── README.md        # 说明文档
```

### 自定义和扩展
- 修改`style.css`自定义界面样式
- 在`renderer.js`中添加新的控制功能
- 在`main.js`中扩展UDP通讯协议

## 许可证

MIT License

## 支持

如有问题或建议，请联系开发团队。
