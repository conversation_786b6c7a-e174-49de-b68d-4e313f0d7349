/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #E1E4EA;
    color: #464879;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* 标题栏 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #464879 0%, #84709B 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 4px 6px rgba(70, 72, 121, 0.2);
}

.header h1 {
    font-size: 24px;
    font-weight: 600;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #ff4757;
    transition: background-color 0.3s ease;
}

.status-indicator.connected {
    background-color: #5D7DB3;
}

/* 连接配置与控制面板 */
.connection-control-panel {
    background: white;
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 12px;
    box-shadow: 0 2px 4px rgba(70, 72, 121, 0.1);
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.left-section {
    flex: 1;
    min-width: 280px;
}

.middle-section {
    flex: 2;
    min-width: 400px;
}

.right-section {
    flex: 1;
    min-width: 250px;
}

.connection-config {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.input-group label {
    font-weight: 500;
    color: #464879;
    font-size: 12px;
}

.input-group input {
    padding: 6px 8px;
    border: 1px solid #D2C3D5;
    border-radius: 4px;
    font-size: 12px;
    transition: border-color 0.3s ease;
    width: 120px;
}

.input-group input:focus {
    outline: none;
    border-color: #5D7DB3;
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background-color: #5D7DB3;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: #464879;
}

.btn-success {
    background-color: #5D7DB3;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background-color: #464879;
}

.btn-danger {
    background-color: #84709B;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background-color: #464879;
}

.btn-warning {
    background-color: #D2C3D5;
    color: #464879;
}

.btn-warning:hover:not(:disabled) {
    background-color: #B6CAD7;
}

.btn-secondary {
    background-color: #B6CAD7;
    color: #464879;
}

.btn-secondary:hover:not(:disabled) {
    background-color: #D2C3D5;
}

.btn-info {
    background-color: #84709B;
    color: white;
}

.btn-info:hover:not(:disabled) {
    background-color: #464879;
}

.btn-small {
    padding: 5px 10px;
    font-size: 12px;
}

.btn-compact {
    padding: 6px 12px;
    font-size: 12px;
    min-width: 70px;
}

/* 控制按钮 */
.control-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
}

.preset-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px;
    background-color: #E1E4EA;
    border-radius: 6px;
    justify-content: center;
}

.preset-controls label {
    font-size: 12px;
    font-weight: 500;
    color: #464879;
}

.preset-controls input[type="range"] {
    width: 120px;
}

#presetValueDisplay {
    font-weight: bold;
    min-width: 25px;
    text-align: center;
    font-size: 12px;
}

/* 主内容区域 */
.main-content {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

/* PWM矩阵区域 */
.matrix-section {
    flex: 2;
    background: white;
    padding: 12px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(70, 72, 121, 0.1);
}

.matrix-section h3 {
    margin-bottom: 8px;
    color: #464879;
    font-size: 14px;
}

/* 侧边栏区域 */
.sidebar-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.matrix-container {
    overflow-x: auto;
}

.matrix-grid {
    display: grid;
    grid-template-columns: repeat(10, 1fr);
    gap: 2px;
    width: 100%;
    max-width: 500px;
}

.pwm-control {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 3px;
    background-color: #E1E4EA;
    border-radius: 3px;
    border: 1px solid transparent;
    transition: all 0.3s ease;
    min-height: 40px;
}

.pwm-control:hover {
    border-color: #5D7DB3;
    background-color: #B6CAD7;
}

.pwm-control.active {
    border-color: #84709B;
    background-color: #D2C3D5;
}

.pwm-control.error {
    border-color: #84709B;
    background-color: #D2C3D5;
}

.motor-label {
    font-size: 8px;
    font-weight: bold;
    color: #464879;
    margin-bottom: 1px;
    line-height: 1;
}

.pwm-input {
    width: 32px;
    padding: 1px;
    border: 1px solid #D2C3D5;
    border-radius: 2px;
    text-align: center;
    font-size: 9px;
    font-weight: bold;
    transition: border-color 0.3s ease;
}

.pwm-input:focus {
    outline: none;
    border-color: #5D7DB3;
}

.pwm-input.error {
    border-color: #84709B;
    background-color: #D2C3D5;
}

/* 状态信息面板 */
.status-info {
    background: white;
    padding: 10px;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(70, 72, 121, 0.1);
    flex: 0 0 auto;
}

.log-panel {
    background: white;
    padding: 10px;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(70, 72, 121, 0.1);
    flex: 1;
    display: flex;
    flex-direction: column;
}

.status-info h4, .log-panel h4 {
    margin-bottom: 8px;
    color: #464879;
    border-bottom: 1px solid #5D7DB3;
    padding-bottom: 3px;
    font-size: 13px;
}

.device-status {
    background-color: #E1E4EA;
    padding: 8px;
    border-radius: 4px;
    border-left: 3px solid #5D7DB3;
    font-size: 11px;
    line-height: 1.3;
}

.device-status p {
    margin: 2px 0;
}

.log-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 6px;
    padding: 6px;
    background-color: #E1E4EA;
    border-radius: 4px;
    flex-wrap: wrap;
}

.log-controls label {
    display: flex;
    align-items: center;
    gap: 3px;
    font-size: 10px;
    cursor: pointer;
}

.log-controls input[type="checkbox"] {
    margin: 0;
    transform: scale(0.8);
}

.activity-log {
    background-color: #E1E4EA;
    padding: 6px;
    border-radius: 4px;
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    font-family: 'Courier New', monospace;
    font-size: 9px;
    border: 1px solid #D2C3D5;
    min-height: 150px;
    max-height: 300px;
    word-wrap: break-word;
    word-break: break-all;
}

.log-entry {
    margin-bottom: 3px;
    padding: 3px 0;
    border-bottom: 1px solid #D2C3D5;
    word-wrap: break-word;
    word-break: break-all;
    overflow-wrap: break-word;
    white-space: pre-wrap;
    max-width: 100%;
}

.log-entry.sent {
    color: #464879;
    background-color: #B6CAD7;
    padding: 3px 6px;
    border-radius: 3px;
}

.log-entry.received {
    color: #5D7DB3;
    background-color: #D2C3D5;
    padding: 3px 6px;
    border-radius: 3px;
}

.log-entry.error {
    color: #84709B;
    background-color: #D2C3D5;
    padding: 3px 6px;
    border-radius: 3px;
}

.log-entry.info {
    color: #464879;
}

.log-entry.hidden {
    display: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .connection-control-panel {
        flex-direction: column;
        gap: 8px;
    }

    .left-section, .middle-section, .right-section {
        min-width: auto;
        flex: none;
    }

    .connection-config {
        justify-content: center;
    }

    .control-buttons {
        justify-content: center;
    }

    .preset-controls {
        justify-content: center;
    }

    .input-group input {
        width: 100px;
    }

    .preset-controls input[type="range"] {
        width: 100px;
    }
    
    .main-content {
        flex-direction: column;
    }

    .matrix-section {
        order: 2;
    }

    .sidebar-section {
        order: 1;
        flex-direction: row;
        gap: 10px;
    }

    .status-info, .log-panel {
        flex: 1;
    }

    .matrix-grid {
        grid-template-columns: repeat(5, 1fr);
        max-width: 300px;
    }

    .pwm-input {
        width: 28px;
        font-size: 8px;
    }

    .motor-label {
        font-size: 7px;
    }

    .activity-log {
        min-height: 120px;
        max-height: 150px;
    }
    
    .preset-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
}
