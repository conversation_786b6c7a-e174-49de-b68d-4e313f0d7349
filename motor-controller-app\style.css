/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* 标题栏 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header h1 {
    font-size: 24px;
    font-weight: 600;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #ff4757;
    transition: background-color 0.3s ease;
}

.status-indicator.connected {
    background-color: #2ed573;
}

/* 连接配置与控制面板 */
.connection-control-panel {
    background: white;
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.left-section {
    flex: 1;
    min-width: 280px;
}

.middle-section {
    flex: 2;
    min-width: 400px;
}

.right-section {
    flex: 1;
    min-width: 250px;
}

.connection-config {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.input-group label {
    font-weight: 500;
    color: #555;
    font-size: 12px;
}

.input-group input {
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 12px;
    transition: border-color 0.3s ease;
    width: 120px;
}

.input-group input:focus {
    outline: none;
    border-color: #667eea;
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background-color: #667eea;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: #5a6fd8;
}

.btn-success {
    background-color: #2ed573;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background-color: #26c965;
}

.btn-danger {
    background-color: #ff4757;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background-color: #ff3742;
}

.btn-warning {
    background-color: #ffa502;
    color: white;
}

.btn-warning:hover:not(:disabled) {
    background-color: #ff9500;
}

.btn-secondary {
    background-color: #57606f;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background-color: #4a5568;
}

.btn-info {
    background-color: #3742fa;
    color: white;
}

.btn-info:hover:not(:disabled) {
    background-color: #2f3542;
}

.btn-small {
    padding: 5px 10px;
    font-size: 12px;
}

.btn-compact {
    padding: 6px 12px;
    font-size: 12px;
    min-width: 70px;
}

/* 控制按钮 */
.control-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
}

.preset-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px;
    background-color: #f8f9fa;
    border-radius: 6px;
    justify-content: center;
}

.preset-controls label {
    font-size: 12px;
    font-weight: 500;
    color: #555;
}

.preset-controls input[type="range"] {
    width: 120px;
}

#presetValueDisplay {
    font-weight: bold;
    min-width: 25px;
    text-align: center;
    font-size: 12px;
}

/* 主内容区域 */
.main-content {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

/* PWM矩阵区域 */
.matrix-section {
    flex: 2;
    background: white;
    padding: 12px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.matrix-section h3 {
    margin-bottom: 8px;
    color: #333;
    font-size: 14px;
}

/* 侧边栏区域 */
.sidebar-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.matrix-container {
    overflow-x: auto;
}

.matrix-grid {
    display: grid;
    grid-template-columns: repeat(10, 1fr);
    gap: 2px;
    width: 100%;
    max-width: 500px;
}

.pwm-control {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 3px;
    background-color: #f8f9fa;
    border-radius: 3px;
    border: 1px solid transparent;
    transition: all 0.3s ease;
    min-height: 40px;
}

.pwm-control:hover {
    border-color: #667eea;
    background-color: #e3f2fd;
}

.pwm-control.active {
    border-color: #2ed573;
    background-color: #e8f5e8;
}

.pwm-control.error {
    border-color: #ff4757;
    background-color: #ffe3e3;
}

.motor-label {
    font-size: 8px;
    font-weight: bold;
    color: #666;
    margin-bottom: 1px;
    line-height: 1;
}

.pwm-input {
    width: 32px;
    padding: 1px;
    border: 1px solid #ddd;
    border-radius: 2px;
    text-align: center;
    font-size: 9px;
    font-weight: bold;
    transition: border-color 0.3s ease;
}

.pwm-input:focus {
    outline: none;
    border-color: #667eea;
}

.pwm-input.error {
    border-color: #ff4757;
    background-color: #ffe3e3;
}

/* 状态信息面板 */
.status-info {
    background: white;
    padding: 10px;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    flex: 0 0 auto;
}

.log-panel {
    background: white;
    padding: 10px;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    flex: 1;
    display: flex;
    flex-direction: column;
}

.status-info h4, .log-panel h4 {
    margin-bottom: 8px;
    color: #333;
    border-bottom: 1px solid #667eea;
    padding-bottom: 3px;
    font-size: 13px;
}

.device-status {
    background-color: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
    border-left: 3px solid #667eea;
    font-size: 11px;
    line-height: 1.3;
}

.device-status p {
    margin: 2px 0;
}

.log-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 6px;
    padding: 6px;
    background-color: #f8f9fa;
    border-radius: 4px;
    flex-wrap: wrap;
}

.log-controls label {
    display: flex;
    align-items: center;
    gap: 3px;
    font-size: 10px;
    cursor: pointer;
}

.log-controls input[type="checkbox"] {
    margin: 0;
    transform: scale(0.8);
}

.activity-log {
    background-color: #f8f9fa;
    padding: 6px;
    border-radius: 4px;
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    font-family: 'Courier New', monospace;
    font-size: 9px;
    border: 1px solid #e0e0e0;
    min-height: 150px;
    max-height: 300px;
    word-wrap: break-word;
    word-break: break-all;
}

.log-entry {
    margin-bottom: 3px;
    padding: 3px 0;
    border-bottom: 1px solid #eee;
    word-wrap: break-word;
    word-break: break-all;
    overflow-wrap: break-word;
    white-space: pre-wrap;
    max-width: 100%;
}

.log-entry.sent {
    color: #3742fa;
    background-color: #f0f0ff;
    padding: 3px 6px;
    border-radius: 3px;
}

.log-entry.received {
    color: #2ed573;
    background-color: #f0fff0;
    padding: 3px 6px;
    border-radius: 3px;
}

.log-entry.error {
    color: #ff4757;
    background-color: #fff0f0;
    padding: 3px 6px;
    border-radius: 3px;
}

.log-entry.info {
    color: #667eea;
}

.log-entry.hidden {
    display: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .connection-control-panel {
        flex-direction: column;
        gap: 8px;
    }

    .left-section, .middle-section, .right-section {
        min-width: auto;
        flex: none;
    }

    .connection-config {
        justify-content: center;
    }

    .control-buttons {
        justify-content: center;
    }

    .preset-controls {
        justify-content: center;
    }

    .input-group input {
        width: 100px;
    }

    .preset-controls input[type="range"] {
        width: 100px;
    }
    
    .main-content {
        flex-direction: column;
    }

    .matrix-section {
        order: 2;
    }

    .sidebar-section {
        order: 1;
        flex-direction: row;
        gap: 10px;
    }

    .status-info, .log-panel {
        flex: 1;
    }

    .matrix-grid {
        grid-template-columns: repeat(5, 1fr);
        max-width: 300px;
    }

    .pwm-input {
        width: 28px;
        font-size: 8px;
    }

    .motor-label {
        font-size: 7px;
    }

    .activity-log {
        min-height: 120px;
        max-height: 150px;
    }
    
    .preset-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
}
