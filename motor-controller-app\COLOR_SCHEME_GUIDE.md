# 配色方案说明

## 设计灵感

本应用采用了优雅的蓝紫色调配色方案，灵感来源于现代设计趋势中的"Color Inspiration #005"。整体色调温和而专业，适合长时间使用的工业控制界面。

## 主要颜色

### 基础色调
- **E1E4EA** - 浅灰蓝色：用于背景和次要区域
- **D2C3D5** - 浅紫色：用于边框和辅助背景
- **84709B** - 中紫色：用于强调和错误状态
- **464879** - 深蓝紫色：用于主要文本和深色按钮
- **5D7DB3** - 蓝色：用于主要按钮和链接
- **B6CAD7** - 浅蓝色：用于悬停状态和次要按钮

## 应用区域

### 1. 整体布局
- **背景色**：E1E4EA（浅灰蓝）
- **主要文本**：464879（深蓝紫）
- **卡片背景**：白色
- **阴影**：464879 透明度20%

### 2. 标题栏
- **背景渐变**：464879 → 84709B
- **文字颜色**：白色
- **连接状态指示器**：5D7DB3（连接时）

### 3. 按钮系统
- **主要按钮**：5D7DB3 背景，悬停时变为 464879
- **成功按钮**：5D7DB3 背景（START按钮）
- **危险按钮**：84709B 背景（STOP按钮）
- **警告按钮**：D2C3D5 背景，464879 文字（Clear All）
- **次要按钮**：B6CAD7 背景，464879 文字
- **信息按钮**：84709B 背景（Reset Base等）

### 4. 输入控件
- **输入框边框**：D2C3D5
- **聚焦状态**：5D7DB3
- **PWM矩阵背景**：E1E4EA
- **激活状态**：D2C3D5 背景，84709B 边框
- **悬停状态**：B6CAD7 背景，5D7DB3 边框

### 5. 日志系统
- **日志背景**：E1E4EA
- **发送消息**：464879 文字，B6CAD7 背景
- **接收消息**：5D7DB3 文字，D2C3D5 背景
- **错误消息**：84709B 文字，D2C3D5 背景
- **信息消息**：464879 文字

### 6. 状态面板
- **面板背景**：E1E4EA
- **标题边框**：5D7DB3
- **左侧强调条**：5D7DB3

## 设计原则

### 1. 层次感
通过不同深度的蓝紫色调建立清晰的视觉层次，从浅色背景到深色文本，确保良好的可读性。

### 2. 一致性
所有交互元素使用统一的配色逻辑：
- 主要操作：蓝色系（5D7DB3）
- 次要操作：浅色系（B6CAD7、D2C3D5）
- 强调/警告：紫色系（84709B）
- 文本：深色（464879）

### 3. 可访问性
- 确保足够的对比度，符合WCAG 2.1 AA标准
- 使用色彩之外的视觉提示（边框、阴影）
- 支持色盲用户的使用

### 4. 专业感
整体色调偏向冷色系，营造专业、可靠的工业控制界面感觉，适合长时间专注使用。

## 技术实现

### CSS变量建议
为了更好的维护性，建议在未来版本中使用CSS变量：

```css
:root {
  --color-bg-primary: #E1E4EA;
  --color-bg-secondary: #D2C3D5;
  --color-accent: #84709B;
  --color-text-primary: #464879;
  --color-button-primary: #5D7DB3;
  --color-button-secondary: #B6CAD7;
}
```

这样可以更容易地调整配色方案或支持主题切换功能。
