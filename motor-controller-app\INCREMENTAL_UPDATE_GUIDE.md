# 增量更新功能使用指南

## 功能概述

增量更新功能可以显著减少网络传输量，只发送与上次相比有变化的PWM值，而不是每次都发送所有100个电机的数据。

## 工作原理

1. **基准状态**：系统维护一个`lastSentValues`数组，记录上次成功发送的PWM值
2. **变化检测**：每次发送前，比较当前PWM值与基准状态
3. **增量发送**：只发送有变化的电机PWM值
4. **基准更新**：发送成功后，更新对应电机的基准值

## 使用方法

### 1. 正常使用流程

```
1. 设置一些电机的PWM值（例如：M0=100, M5=200）
2. 点击"Send PWM" → 发送2个变化的值
3. 修改部分值（例如：M0=150, M10=50）
4. 再次点击"Send PWM" → 只发送2个变化的值（M0和M10）
```

### 2. 重置基准

如果需要重新设置比较基准：
- 点击"Reset Base"按钮
- 当前所有PWM值将被设为新的基准状态
- 下次发送时将基于新基准进行比较

### 3. 日志信息

增量更新会在通讯日志中显示详细信息：
```
Incremental update: 3 changes detected
Changes: M0: 0→100, M5: 0→200, M10: 50→0
→ Sending: SET,0,100
← Received: {"status":"success","action":"set","motor":0,"pwm":100}
→ Sending: SET,5,200
← Received: {"status":"success","action":"set","motor":5,"pwm":200}
→ Sending: SET,10,0
← Received: {"status":"success","action":"set","motor":10,"pwm":0}
PWM incremental send completed: 3 success, 0 errors
```

## 优势

1. **减少网络负载**：只发送变化的数据，而不是全部100个值
2. **提高效率**：减少UDP包数量，降低网络拥塞风险
3. **清晰的反馈**：日志显示具体哪些电机发生了变化
4. **智能比较**：自动跟踪发送状态，无需手动管理

## 注意事项

1. **初始状态**：程序启动时，所有电机的基准值为0
2. **发送失败**：如果某个电机的SET命令发送失败，该电机的基准值不会更新
3. **重置时机**：在大幅修改PWM配置后，建议使用"Reset Base"重置基准
4. **预设功能**：使用"Apply to All"或"Clear All"后，建议重置基准以获得最佳效果

## 示例场景

### 场景1：逐步调试
```
1. 设置M0=100 → 发送1个值
2. 设置M1=150 → 发送1个值  
3. 调整M0=120 → 发送1个值
4. 关闭M1=0 → 发送1个值
```

### 场景2：批量设置后微调
```
1. Apply to All: 128 → 重置基准
2. 调整M5=200, M10=50 → 发送2个值
3. 再调整M5=180 → 发送1个值
```

这种增量更新机制特别适合需要频繁微调电机参数的应用场景。
