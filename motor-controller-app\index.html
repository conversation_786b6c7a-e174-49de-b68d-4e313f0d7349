<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Motor Controller</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <!-- 标题栏 -->
        <header class="header">
            <h1>ESP32S3 Motor Controller</h1>
            <div class="connection-status" id="connectionStatus">
                <span class="status-indicator" id="statusIndicator"></span>
                <span id="statusText">Disconnected</span>
            </div>
        </header>

        <!-- 连接配置与控制面板 -->
        <section class="connection-control-panel">
            <div class="left-section">
                <div class="connection-config">
                    <div class="input-group">
                        <label for="ipAddress">IP:</label>
                        <input type="text" id="ipAddress" value="*************" placeholder="*************">
                    </div>
                    <div class="input-group">
                        <label for="port">Port:</label>
                        <input type="number" id="port" value="3000" placeholder="3000">
                    </div>
                    <button id="testConnection" class="btn btn-secondary btn-compact">连接</button>
                    <button id="debugTest" class="btn btn-info btn-compact">断开</button>
                </div>
            </div>

            <div class="middle-section">
                <div class="control-buttons">
                    <button id="startMotors" class="btn btn-success btn-compact" disabled>START</button>
                    <button id="stopMotors" class="btn btn-danger btn-compact" disabled>STOP</button>
                    <button id="sendPWM" class="btn btn-primary btn-compact" disabled>Send PWM</button>
                    <button id="clearAll" class="btn btn-warning btn-compact">Clear All</button>
                    <button id="resetIncremental" class="btn btn-info btn-compact">Reset Base</button>
                    <button id="sendZeros" class="btn btn-secondary btn-compact" disabled>Send Zeros</button>
                </div>
            </div>

            <div class="right-section">
                <div class="preset-controls">
                    <label for="presetValue">Preset:</label>
                    <input type="range" id="presetValue" min="0" max="255" value="128">
                    <span id="presetValueDisplay">128</span>
                    <button id="applyPreset" class="btn btn-info btn-compact">Apply to All</button>
                </div>
            </div>
        </section>

        <!-- 主内容区域 -->
        <section class="main-content">
            <!-- PWM控制矩阵 -->
            <div class="matrix-section">
                <h3>PWM Control Matrix (10x10)</h3>
                <div class="matrix-container">
                    <div class="matrix-grid" id="pwmMatrix">
                        <!-- 10x10 PWM控制器将通过JavaScript生成 -->
                    </div>
                </div>
            </div>

            <!-- 状态信息面板 -->
            <div class="sidebar-section">
                <div class="status-info">
                    <h4>Device Status</h4>
                    <div id="deviceStatus" class="device-status">
                        <p>No device information available</p>
                    </div>
                </div>

                <div class="log-panel">
                    <h4>Communication Log</h4>
                    <div class="log-controls">
                        <label>
                            <input type="checkbox" id="showSentMessages" checked> Sent
                        </label>
                        <label>
                            <input type="checkbox" id="showReceivedMessages" checked> Received
                        </label>
                        <label>
                            <input type="checkbox" id="showErrors" checked> Errors
                        </label>
                        <button id="clearLog" class="btn btn-small">Clear</button>
                    </div>
                    <div id="activityLog" class="activity-log">
                        <p class="log-entry info">Application started</p>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <script src="renderer.js"></script>
</body>
</html>
